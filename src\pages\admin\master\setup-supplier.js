const { ipc<PERSON>ender<PERSON> } = require('electron');

// Sidebar functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

function handleNavClick(page) {
    switch (page) {
        case 'dashboard':
            ipc<PERSON>enderer.invoke('navigate-to-admin-section', 'dashboard');
            break;
        case 'master':
            // Toggle master dropdown
            const masterSubitems = document.getElementById('master-subitems');
            masterSubitems.classList.toggle('hidden');
            break;
        case 'setup-location':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-location.html');
            break;
        case 'setup-category':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-category.html');
            break;
        case 'setup-supplier':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-supplier.html');
            break;
        case 'setup-product':
            ipc<PERSON>enderer.invoke('navigate-to-page', 'admin/master/setup-product.html');
            break;
        case 'reports':
            ipc<PERSON>enderer.invoke('navigate-to-admin-section', 'reports');
            break;
        case 'transactions':
            ipcRenderer.invoke('navigate-to-admin-section', 'transactions');
            break;
        case 'wholesale':
            ipcRenderer.invoke('navigate-to-admin-section', 'wholesale');
            break;
        case 'user-management':
            ipcRenderer.invoke('navigate-to-admin-section', 'user-management');
            break;
        case 'back-to-pos':
            ipcRenderer.invoke('navigate-to', 'pos');
            break;
        default:
            console.log('Unknown navigation:', page);
    }
}

// Suppliers data - loaded from database
let suppliers = [];

// Form data object
let formData = {
    name: "",
    address1: "",
    address2: "",
    city: "",
    state: "",
    zipCode: "",
    telephone: "",
    fax: "",
    email: "",
    salesRep: "",
    salesRepPhone: "",
    retailWebsite: "",
    wholesaleWebsite: ""
};

let selectedSupplierIndex = -1;
let isEditMode = false;
let editingSupplierId = null;

// Message display function
function showMessage(message, type = 'info') {
    // Remove existing message
    const existingMessage = document.querySelector('.message-display');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-display message-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#10b981';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#ef4444';
            break;
        case 'info':
            messageDiv.style.backgroundColor = '#3b82f6';
            break;
        default:
            messageDiv.style.backgroundColor = '#6b7280';
    }

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 4000);
}

// Add CSS animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);

    // Load suppliers from database
    await loadSuppliers();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Form functions
function getFormData() {
    return {
        name: document.getElementById('name').value,
        address1: document.getElementById('address1').value,
        address2: document.getElementById('address2').value,
        city: document.getElementById('city').value,
        state: document.getElementById('state').value,
        zipCode: document.getElementById('zipCode').value,
        telephone: document.getElementById('telephone').value,
        fax: document.getElementById('fax').value,
        email: document.getElementById('email').value,
        salesRep: document.getElementById('salesRep').value,
        salesRepPhone: document.getElementById('salesRepPhone').value,
        retailWebsite: document.getElementById('retailWebsite').value,
        wholesaleWebsite: document.getElementById('wholesaleWebsite').value
    };
}

function setFormData(data) {
    document.getElementById('name').value = data.name || '';
    document.getElementById('address1').value = data.address1 || '';
    document.getElementById('address2').value = data.address2 || '';
    document.getElementById('city').value = data.city || '';
    document.getElementById('state').value = data.state || '';
    document.getElementById('zipCode').value = data.zipCode || '';
    document.getElementById('telephone').value = data.telephone || '';
    document.getElementById('fax').value = data.fax || '';
    document.getElementById('email').value = data.email || '';
    document.getElementById('salesRep').value = data.salesRep || '';
    document.getElementById('salesRepPhone').value = data.salesRepPhone || '';
    document.getElementById('retailWebsite').value = data.retailWebsite || '';
    document.getElementById('wholesaleWebsite').value = data.wholesaleWebsite || '';
}

async function saveSupplier() {
    const data = getFormData();

    if (!data.name) {
        showMessage('Please fill in required field: Supplier Name', 'error');
        return;
    }

    try {
        // Prepare supplier data for database
        const supplierData = {
            name: data.name,
            address1: data.address1,
            address2: data.address2,
            city: data.city,
            state: data.state,
            zip_code: data.zipCode,
            telephone: data.telephone,
            fax: data.fax,
            email: data.email,
            sales_rep: data.salesRep,
            sales_rep_phone: data.salesRepPhone,
            retail_website: data.retailWebsite,
            wholesale_website: data.wholesaleWebsite,
            status: 'active'
        };

        let result;
        if (isEditMode && editingSupplierId) {
            // Update existing supplier
            result = await ipcRenderer.invoke('update-supplier', editingSupplierId, supplierData);
            if (result.success) {
                showMessage('Supplier updated successfully!', 'success');
                exitEditMode();
            } else {
                showMessage('Error updating supplier: ' + result.message, 'error');
            }
        } else {
            // Check if supplier already exists for new creation
            const existingSupplier = await ipcRenderer.invoke('get-supplier-by-name', data.name);
            if (existingSupplier.success && existingSupplier.supplier) {
                showMessage('Supplier with this name already exists!', 'error');
                return;
            }

            // Create new supplier
            result = await ipcRenderer.invoke('create-supplier', supplierData);
            if (result.success) {
                showMessage('Supplier created successfully!', 'success');
            } else {
                showMessage('Error creating supplier: ' + result.message, 'error');
            }
        }

        if (result.success) {
            await loadSuppliers();
            clearForm();
        }

        console.log('Supplier operation result:', result);
    } catch (error) {
        console.error('Error saving supplier:', error);
        showMessage('Error saving supplier: ' + error.message, 'error');
    }
}

// Exit edit mode
function exitEditMode() {
    isEditMode = false;
    editingSupplierId = null;
    const saveBtn = document.querySelector('.btn-green');
    if (saveBtn) {
        saveBtn.textContent = 'SAVE';
        saveBtn.style.backgroundColor = '#10b981';
    }
}

function clearForm() {
    setFormData({});
    selectedSupplierIndex = -1;
    exitEditMode(); // Exit edit mode when clearing form

    // Clear table selection
    const rows = document.querySelectorAll('#supplierTableBody tr');
    rows.forEach(row => row.classList.remove('selected'));

    console.log('Form cleared');
}

async function deleteSupplier() {
    const supplierName = document.getElementById('name').value;

    if (!supplierName) {
        alert('Please select a supplier to delete');
        return;
    }

    try {
        // Find the supplier by name
        const existingSupplier = await ipcRenderer.invoke('get-supplier-by-name', supplierName);

        if (!existingSupplier.success || !existingSupplier.supplier) {
            alert('Supplier not found: ' + supplierName);
            return;
        }

        if (confirm('Are you sure you want to delete this supplier?\n\nName: ' + supplierName)) {
            const result = await ipcRenderer.invoke('delete-supplier', existingSupplier.supplier.id);

            if (result.success) {
                alert('Supplier deleted successfully!');
                await loadSuppliers();
                clearForm();
            } else {
                alert('Error deleting supplier: ' + result.message);
            }
        }
    } catch (error) {
        console.error('Error deleting supplier:', error);
        alert('Error deleting supplier: ' + error.message);
    }
}

async function loadSuppliers() {
    try {
        const result = await ipcRenderer.invoke('get-all-suppliers');

        if (result.success) {
            suppliers = result.suppliers.map(sup => ({
                supplierName: sup.name,
                telephone: sup.telephone || '',
                fax: sup.fax || '',
                email: sup.email || '',
                salesRep: sup.sales_rep || '',
                repPhone: sup.sales_rep_phone || '',
                id: sup.id,
                fullData: sup
            }));

            renderSupplierTable();
        } else {
            console.error('Error loading suppliers:', result.message);
        }
    } catch (error) {
        console.error('Error loading suppliers:', error);
    }
}

function selectSupplier(index) {
    selectedSupplierIndex = index;
    const supplier = suppliers[index];

    if (supplier && supplier.fullData) {
        const fullData = supplier.fullData;
        setFormData({
            name: fullData.name,
            address1: fullData.address1,
            address2: fullData.address2,
            city: fullData.city,
            state: fullData.state,
            zipCode: fullData.zip_code,
            telephone: fullData.telephone,
            fax: fullData.fax,
            email: fullData.email,
            salesRep: fullData.sales_rep,
            salesRepPhone: fullData.sales_rep_phone,
            retailWebsite: fullData.retail_website,
            wholesaleWebsite: fullData.wholesale_website
        });

        // Update table selection
        const rows = document.querySelectorAll('#supplierTableBody tr');
        rows.forEach((row, i) => {
            if (i === index) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
    }
}

function renderSupplierTable() {
    const tbody = document.getElementById('supplierTableBody');
    tbody.innerHTML = '';

    suppliers.forEach((supplier, index) => {
        const row = document.createElement('tr');
        row.style.cursor = 'pointer';

        row.innerHTML = `
            <td class="font-medium">${supplier.supplierName}</td>
            <td class="text-center">${supplier.telephone || ''}</td>
            <td class="text-center">${supplier.fax || ''}</td>
            <td class="text-center">${supplier.email || ''}</td>
            <td class="text-center">${supplier.salesRep || ''}</td>
            <td class="text-center">${supplier.repPhone || ''}</td>
            <td class="text-center">
                <div class="flex gap-2 justify-center">
                    <button class="btn btn-blue btn-small" onclick="editSupplier(${index})" title="Edit Supplier">
                        Edit
                    </button>
                    <button class="btn btn-red btn-small" onclick="confirmDeleteSupplier(${index})" title="Delete Supplier">
                        Delete
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// Edit supplier function
function editSupplier(index) {
    const supplier = suppliers[index];
    if (supplier && supplier.fullData) {
        // Set edit mode
        isEditMode = true;
        editingSupplierId = supplier.fullData.id;

        // Load supplier data into form
        setFormData({
            name: supplier.fullData.name,
            address1: supplier.fullData.address1 || '',
            address2: supplier.fullData.address2 || '',
            city: supplier.fullData.city || '',
            state: supplier.fullData.state || '',
            zipCode: supplier.fullData.zip_code || '',
            telephone: supplier.fullData.telephone || '',
            fax: supplier.fullData.fax || '',
            email: supplier.fullData.email || '',
            salesRep: supplier.fullData.sales_rep || '',
            salesRepPhone: supplier.fullData.sales_rep_phone || '',
            retailWebsite: supplier.fullData.retail_website || '',
            wholesaleWebsite: supplier.fullData.wholesale_website || ''
        });

        // Change save button to update mode
        const saveBtn = document.querySelector('.btn-green');
        if (saveBtn) {
            saveBtn.textContent = 'UPDATE';
            saveBtn.style.backgroundColor = '#f59e0b';
        }

        showMessage(`Editing supplier: ${supplier.fullData.name}`, 'info');

        // Scroll to form
        document.querySelector('.form-card').scrollIntoView({ behavior: 'smooth' });
    }
}

// Confirm delete function
function confirmDeleteSupplier(index) {
    const supplier = suppliers[index];
    if (supplier && supplier.fullData && confirm(`Are you sure you want to delete supplier "${supplier.fullData.name}"?`)) {
        deleteSupplierById(supplier.fullData.id);
    }
}

// Delete supplier by ID
async function deleteSupplierById(supplierId) {
    try {
        const result = await ipcRenderer.invoke('delete-supplier', supplierId);

        if (result.success) {
            showMessage('Supplier deleted successfully!', 'success');
            await loadSuppliers();
            clearForm();
        } else {
            showMessage('Error deleting supplier: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error deleting supplier:', error);
        showMessage('Error deleting supplier: ' + error.message, 'error');
    }
}

function updateSupplierField(index, field, value) {
    if (suppliers[index]) {
        suppliers[index][field] = value;
    }
}
