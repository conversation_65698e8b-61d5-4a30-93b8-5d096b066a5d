const { ipc<PERSON>ender<PERSON> } = require('electron');

// Sidebar functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

function handleNavClick(page) {
    switch (page) {
        case 'dashboard':
            ipc<PERSON>enderer.invoke('navigate-to-admin-section', 'dashboard');
            break;
        case 'master':
            // Toggle master dropdown
            const masterSubitems = document.getElementById('master-subitems');
            masterSubitems.classList.toggle('hidden');
            break;
        case 'setup-location':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-location.html');
            break;
        case 'setup-category':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-category.html');
            break;
        case 'setup-supplier':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-supplier.html');
            break;
        case 'setup-product':
            ipc<PERSON>enderer.invoke('navigate-to-page', 'admin/master/setup-product.html');
            break;
        case 'reports':
            ipc<PERSON>enderer.invoke('navigate-to-admin-section', 'reports');
            break;
        case 'transactions':
            ipcRenderer.invoke('navigate-to-admin-section', 'transactions');
            break;
        case 'wholesale':
            ipcRenderer.invoke('navigate-to-admin-section', 'wholesale');
            break;
        case 'user-management':
            ipcRenderer.invoke('navigate-to-admin-section', 'user-management');
            break;
        case 'back-to-pos':
            ipcRenderer.invoke('navigate-to', 'pos');
            break;
        default:
            console.log('Unknown navigation:', page);
    }
}

// Categories data - loaded from database
let categories = [];
let hierarchyData = [];
let isEditMode = false;
let editingCategoryId = null;

// Message display function
function showMessage(message, type = 'info') {
    // Remove existing message
    const existingMessage = document.querySelector('.message-display');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-display message-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#10b981';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#ef4444';
            break;
        case 'info':
            messageDiv.style.backgroundColor = '#3b82f6';
            break;
        default:
            messageDiv.style.backgroundColor = '#6b7280';
    }

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 4000);
}

// Add CSS animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    updateTime();
    setInterval(updateTime, 1000);

    // Load categories from database
    await loadCategories();
    renderCategoryHierarchy();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Category functions
async function addCategory() {
    const categoryName = document.getElementById('categoryName').value;
    const categoryCode = document.getElementById('categoryCode').value;
    const parentCategory = document.getElementById('parentCategory').value;
    const categoryDescription = document.getElementById('categoryDescription').value;
    const displayOrder = document.getElementById('displayOrder').value;
    const categoryStatus = document.getElementById('categoryStatus').value;

    if (!categoryName || !categoryCode) {
        alert('Please fill in required fields: Category Name and Category Code');
        return;
    }

    try {
        // Check if code already exists
        const existingCategory = await ipcRenderer.invoke('get-category-by-code', categoryCode);

        if (existingCategory.success && existingCategory.category) {
            // Update existing category
            const categoryData = {
                code: categoryCode,
                name: categoryName,
                parent_id: parentCategory ? parseInt(parentCategory) : null,
                description: categoryDescription,
                display_order: parseInt(displayOrder) || 0,
                status: categoryStatus
            };

            const result = await ipcRenderer.invoke('update-category', existingCategory.category.id, categoryData);

            if (result.success) {
                alert('Category updated successfully!');
                await loadCategories();
                clearForm();
            } else {
                alert('Error updating category: ' + result.message);
            }
        } else {
            // Create new category
            const categoryData = {
                code: categoryCode,
                name: categoryName,
                parent_id: parentCategory ? parseInt(parentCategory) : null,
                description: categoryDescription,
                display_order: parseInt(displayOrder) || 0,
                status: categoryStatus
            };

            const result = await ipcRenderer.invoke('create-category', categoryData);

            if (result.success) {
                alert('Category created successfully!');
                await loadCategories();
                clearForm();
            } else {
                alert('Error creating category: ' + result.message);
            }
        }
    } catch (error) {
        console.error('Error saving category:', error);
        alert('Error saving category: ' + error.message);
    }
}

// Save category function
async function saveCategory() {
    const categoryData = {
        name: document.getElementById('categoryName').value.trim(),
        code: document.getElementById('categoryCode').value.trim(),
        parent_id: document.getElementById('parentCategory').value || null,
        description: document.getElementById('categoryDescription').value.trim(),
        display_order: parseInt(document.getElementById('displayOrder').value) || 0,
        status: document.getElementById('categoryStatus').value
    };

    // Validation
    if (!categoryData.name || !categoryData.code) {
        showMessage('Please fill in Category Name and Category Code fields.', 'error');
        return;
    }

    try {
        let result;
        if (isEditMode && editingCategoryId) {
            // Update existing category
            result = await ipcRenderer.invoke('update-category', editingCategoryId, categoryData);
            if (result.success) {
                showMessage('Category updated successfully!', 'success');
                exitEditMode();
            } else {
                showMessage('Error updating category: ' + result.message, 'error');
            }
        } else {
            // Check if category code already exists for new creation
            const existingCategory = await ipcRenderer.invoke('get-category-by-code', categoryData.code);
            if (existingCategory.success && existingCategory.category) {
                showMessage('Category with this code already exists!', 'error');
                return;
            }

            // Create new category
            result = await ipcRenderer.invoke('create-category', categoryData);
            if (result.success) {
                showMessage('Category created successfully!', 'success');
            } else {
                showMessage('Error creating category: ' + result.message, 'error');
            }
        }

        if (result.success) {
            await loadCategories();
            await loadParentCategoryDropdown();
            clearForm();
        }
    } catch (error) {
        console.error('Error saving category:', error);
        showMessage('Error saving category: ' + error.message, 'error');
    }
}

// Exit edit mode
function exitEditMode() {
    isEditMode = false;
    editingCategoryId = null;
    const saveBtn = document.querySelector('.btn-green');
    if (saveBtn) {
        saveBtn.textContent = 'SAVE';
        saveBtn.style.backgroundColor = '#10b981';
    }
}

function clearForm() {
    document.getElementById('categoryName').value = '';
    document.getElementById('categoryCode').value = '';
    document.getElementById('parentCategory').value = '';
    document.getElementById('categoryDescription').value = '';
    document.getElementById('displayOrder').value = '';
    document.getElementById('categoryStatus').value = 'active';
    exitEditMode(); // Exit edit mode when clearing form
}

function editCategory(code) {
    const category = categories.find(cat => cat.code === code);
    if (category) {
        // Set edit mode
        isEditMode = true;
        editingCategoryId = category.id;

        // Load category data into form
        document.getElementById('categoryName').value = category.name;
        document.getElementById('categoryCode').value = category.code;
        document.getElementById('parentCategory').value = category.parent_id || "";
        document.getElementById('categoryDescription').value = category.description || '';
        document.getElementById('displayOrder').value = category.display_order || '';
        document.getElementById('categoryStatus').value = category.status;

        // Change save button to update mode
        const saveBtn = document.querySelector('.btn-green');
        if (saveBtn) {
            saveBtn.textContent = 'UPDATE';
            saveBtn.style.backgroundColor = '#f59e0b';
        }

        showMessage(`Editing category: ${category.name}`, 'info');

        // Scroll to form
        document.querySelector('.form-card').scrollIntoView({ behavior: 'smooth' });
    }
}

async function deleteCategory(code) {
    try {
        const existingCategory = await ipcRenderer.invoke('get-category-by-code', code);

        if (!existingCategory.success || !existingCategory.category) {
            showMessage('Category not found', 'error');
            return;
        }

        if (confirm('Are you sure you want to delete this category?\n\nCode: ' + code + '\nName: ' + existingCategory.category.name)) {
            const result = await ipcRenderer.invoke('delete-category', existingCategory.category.id);

            if (result.success) {
                showMessage('Category deleted successfully!', 'success');
                await loadCategories();
                await loadParentCategoryDropdown();
                clearForm();
            } else {
                showMessage('Error deleting category: ' + result.message, 'error');
            }
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        showMessage('Error deleting category: ' + error.message, 'error');
    }
}

async function loadCategories() {
    try {
        const result = await ipcRenderer.invoke('get-all-categories');

        if (result.success) {
            categories = result.categories.map(cat => ({
                code: cat.code,
                name: cat.name,
                parent: cat.parent_name || "-",
                productsCount: 0, // Will be calculated later
                status: cat.status,
                description: cat.description,
                displayOrder: cat.display_order,
                id: cat.id,
                parent_id: cat.parent_id
            }));

            renderCategoryTable();
            renderCategoryHierarchy();
            await loadParentCategoryDropdown();
        } else {
            console.error('Error loading categories:', result.message);
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

async function loadParentCategoryDropdown() {
    try {
        const result = await ipcRenderer.invoke('get-parent-categories');

        if (result.success) {
            const parentSelect = document.getElementById('parentCategory');
            parentSelect.innerHTML = '<option value="">Select Parent Category (Optional)</option>';

            result.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                parentSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading parent categories:', error);
    }
}

function importCategories() {
    console.log('Import categories functionality');
    alert('Import categories functionality will be implemented');
}

function exportCategories() {
    console.log('Export categories functionality');
    alert('Export categories functionality will be implemented');
}

function renderCategoryHierarchy() {
    const container = document.getElementById('categoryHierarchy');
    container.innerHTML = '';

    // Build hierarchy from loaded categories
    const parentCategories = categories.filter(cat => !cat.parent_id);

    parentCategories.forEach(parent => {
        const children = categories.filter(cat => cat.parent_id === parent.id);

        const hierarchyItem = document.createElement('div');
        hierarchyItem.className = 'hierarchy-item';

        hierarchyItem.innerHTML = `
            <div class="hierarchy-title">${parent.name}</div>
            <div class="hierarchy-children">
                ${children.map(child => `<div class="hierarchy-child">• ${child.name}</div>`).join('')}
            </div>
        `;

        container.appendChild(hierarchyItem);
    });
}

function renderCategoryTable() {
    const tbody = document.getElementById('categoryTableBody');
    tbody.innerHTML = '';

    categories.forEach(category => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #2563eb; font-weight: 500;">${category.code}</td>
            <td style="color: #1f2937;">${category.name}</td>
            <td style="color: #6b7280;">${category.parent}</td>
            <td style="color: #6b7280;">${category.productsCount} products</td>
            <td>
                <span class="status-badge status-active">
                    ${category.status.charAt(0).toUpperCase() + category.status.slice(1)}
                </span>
            </td>
            <td>
                <div class="flex gap-2">
                    <button class="btn btn-blue btn-small" onclick="editCategory('${category.code}')">
                        Edit
                    </button>
                    <button class="btn btn-red btn-small" onclick="deleteCategory('${category.code}')">
                        Delete
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}
